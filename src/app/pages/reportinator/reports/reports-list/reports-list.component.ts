import {Component, OnInit} from '@angular/core';
import { UtilsService } from 'src/app/@core/utils/utils.service';
import {TranslateService} from "@ngx-translate/core";
import {ReportinatorService} from "../../../../@shared/services/reportinator.service";
import {FormControl, FormGroup} from "@angular/forms";
import {BehaviorSubject, pairwise, Subject} from "rxjs";
import {Ngb<PERSON>odal, NgbModalRef} from "@ng-bootstrap/ng-bootstrap";
import {PaginationResponse} from "../../../../@shared/models/response.interfaces";
import {environment} from "../../../../../environments/environment";
import {ReportResponse, TemplateResponse} from "../../../../@shared/models/reportinator.interfaces";
import {_REP_REP_1, _REP_TMP_0, _REP_TMP_1} from "../../../../@shared/models/input.interfaces";
import {Column} from "../../../../@shared/components/advanced-table/advanced-table.component";
import {DatePipe} from "@angular/common";
import {SortEvent} from "../../../../@shared/components/advanced-table/sortable.directive";
import {Router} from "@angular/router";
import {NewReportModalComponent} from "./_modals/new-report-modal/new-report-modal.component";
import {PaginationContainer} from "../../../../@shared/models/global.interfaces";
import {HeaderFiltersContainer} from "../../../../@shared/components/tablerino-header/tablerino-header.component";
import {WorkOrderRow} from "../../../work-orders/work-orders-overview/work-orders-overview.component";
import {TablerinoColumn, TablerinoSettings} from "../../../../@shared/components/tablerino/tablerino.component";
import {StandardImports} from "../../../../@shared/global_import";
import {TablerinoCompleteComponent} from "../../../../@shared/components/tablerino-complete/tablerino-complete.component";
import {PageHeaderComponent} from "../../../../@shared/components/page-header/page-header.component";

export interface ReportRow extends ReportResponse{
  selected: boolean;
}

@Component({
    selector: 'reportinator-templates-list',
    templateUrl: './reports-list.template.html',
    styleUrls: [],
    standalone: true,
  imports: [StandardImports, TablerinoCompleteComponent, PageHeaderComponent]
})
export class ReportsListComponent implements OnInit {

  columns: Array<Column> = [];
  sortFields = [
    {key: 'order_id', text: 'orders.orderNumber'},
    {key: 'execution_at', text: 'orders.orderDate'},
    {key: 'created_at', text: 'orders.createdDate'},
  ]

  private readonly destroy$: Subject<void> = new Subject<void>();
  modalReference: NgbModalRef;
  searchString: string = '';
  dataResponse: PaginationResponse<ReportResponse[]>;
  reportRows: ReportRow[] = [];
  settings: TablerinoSettings = {
    clickableRows: true,
    routerLinkKey: 'report_id',
    routerLinkPrefix: '/reportinator/reports/details/'
  }

  loading: boolean = false;
  columnsSubject: BehaviorSubject<TablerinoColumn[]> = new BehaviorSubject<TablerinoColumn[]>([]);
  paginationSubject: BehaviorSubject<PaginationContainer> = new BehaviorSubject<PaginationContainer>({page: 1, limit: 25, paginate: 1, totalPages: 0, totalItems: 0});
  headerFiltersContainerSubject: BehaviorSubject<HeaderFiltersContainer> = new BehaviorSubject<HeaderFiltersContainer>({filters: [], init: true});
  selectedRowsSubject: BehaviorSubject<ReportRow[]> = new BehaviorSubject<ReportRow[]>([]);
  cancelCurrentRequest$ = new Subject<void>();


  constructor(
    private reportinatorService: ReportinatorService,
    public utilsService: UtilsService,
    private translate: TranslateService,
    private router: Router,
    private modalService: NgbModal) {
  }

  ngOnInit() {
    this.initializeColumns();
    this.initializeHeaderFilters();
    this.getReports();
    this.paginationSubject.pipe(pairwise()).subscribe((paginationDetails) => {
      if (paginationDetails[0].page !== paginationDetails[1].page) {
        this.getReports();
      }
    });
    this.headerFiltersContainerSubject.subscribe((headerFilters) => {
      if (!headerFilters.init) {
        this.getReports();
      }
    });
  }

  getReports(searchTerm: string = '') {
    this.loading = true;
    this.cancelCurrentRequest$.next();

    let filters = this.headerFiltersContainerSubject.value.filters;
    let sortColumn = this.columnsSubject.value.find(col => col.sortedAsc || col.sortedDesc) || null;
    let sortKey: string = sortColumn?.name || 'created_at';
    let sortDirection: 'asc' | 'desc' = sortColumn ? (sortColumn.sortedAsc ? 'asc' : 'desc') : 'desc';

    let params: _REP_REP_1 = {
      page: this.paginationSubject.value.page,
      limit: this.paginationSubject.value.limit,
      search_string: searchTerm,
      order_by: sortKey,
      order_direction: sortDirection,
    }
    this.reportinatorService.getReports(params).subscribe((res) => {
      this.dataResponse = res;
      this.paginationSubject.next({
        ...this.paginationSubject.value,
        totalItems: res.total_items,
        totalPages: res.total_pages
      });
      this.reportRows = res.data.map((wo) => {
        return {
          ...wo,
          selected: false,
        }
      });
      this.loading = false;
    }, error => {
      this.loading = false
    });
  }

  initializeColumns() {
    this.columnsSubject.next([
      {
        name: 'ID',
        labelKey: this.translate.instant('reportinator.reports.list.id'),
        formatter: (rep: ReportResponse) => `${rep.report_id}`,
        sort: true,
        visible: true,
      },
      {
        name: 'Customer',
        labelKey: this.translate.instant('reportinator.reports.list.customer'),
        formatter: (rep: ReportResponse) => `${rep.service_recipient ? rep.service_recipient.name : ''}`,
        sort: true,
        visible: true,
      },
      {
        name: 'Address',
        labelKey: this.translate.instant('reportinator.reports.list.address'),
        formatter: (rep: ReportResponse) => `${rep.address ? rep.address.display : ''}`,
        sort: true,
        visible: true,
      },
      {
        name: 'Appraiser',
        labelKey: this.translate.instant('reportinator.reports.list.appraiser'),
        formatter: (rep: ReportResponse) => `${rep.crew.length > 0 ? rep.crew[0].full_name : ''}`,
        sort: true,
        visible: true,
      },
      {
        name: 'Deadline',
        labelKey: this.translate.instant('reportinator.reports.list.deadlineAt'),
        formatter: (rep: ReportResponse) => `${this.utilsService.displayDate(rep.delivery_at)}`,
        sort: true,
        visible: true,
      }
    ])
  };

  initializeHeaderFilters() {}

  onNewReportClicked() {
    let modalRef = this.modalService.open(NewReportModalComponent, {});
  }

  rowClicked(row: ReportRow) {
    this.router.navigate(['/reportinator/reports/details/', row.report_id]);
  }
}
