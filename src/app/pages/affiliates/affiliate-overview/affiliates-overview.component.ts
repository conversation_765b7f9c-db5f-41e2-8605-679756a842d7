import {Component, Input, OnInit} from '@angular/core';
import { Ngb<PERSON>odal, NgbModalRef } from "@ng-bootstrap/ng-bootstrap";
import {ActivatedRoute, Router} from "@angular/router";
import {_CRM_AFF_1} from 'src/app/@shared/models/input.interfaces';
import {SortEvent} from "../../../@shared/components/advanced-table/sortable.directive";
import {BehaviorSubject, debounceTime, pairwise, Subject} from "rxjs";
import {takeUntil} from "rxjs/operators";
import {AffiliateService} from "../../../@shared/services/affiliate.service";
import {AffiliateResponse} from "../../../@shared/models/affiliate.interfaces";
import {AddAffiliateModalComponent} from "./_modals/add-affiliate-modal/add-affiliate-modal.component";
import {PaginationContainer} from "../../../@shared/models/global.interfaces";
import {TablerinoColumn, TablerinoSettings} from "../../../@shared/components/tablerino/tablerino.component";
import {HeaderFilterComponent, HeaderFiltersContainer} from "../../../@shared/components/tablerino-header/tablerino-header.component";
import {StorageService} from "../../../@core/services/storage.service";
import {PrivateCustomersModalComponent} from "../../../@shared/components/private-customers-modal/private-customers-modal.component";
import {ImportCustomersModalComponent} from "./_modals/import-customers-modal/import-customers-modal.component";
import {StandardImports} from "../../../@shared/global_import";
import {PageHeaderComponent} from "../../../@shared/components/page-header/page-header.component";
import {TablerinoCompleteComponent} from "../../../@shared/components/tablerino-complete/tablerino-complete.component";
import {AuthService} from "../../../@core/services/auth.service";

@Component({
    selector: 'app-affiliates-overview',
    templateUrl: './affiliates-overview.component.html',
    styleUrls: ['./affiliates-overview.component.css'],
    standalone: true,
    imports: [StandardImports, PageHeaderComponent, TablerinoCompleteComponent]
})
export class AffiliatesOverviewComponent implements OnInit {

  @Input() private: boolean = false;

  sortFields = [
    {key: 'order_id', text: 'orders.orderNumber'},
    {key: 'execution_at', text: 'orders.orderDate'},
    {key: 'created_at', text: 'orders.createdDate'},
  ]

  private readonly destroy$: Subject<void> = new Subject<void>();

  columnsSubject: BehaviorSubject<TablerinoColumn[]> = new BehaviorSubject<TablerinoColumn[]>([]);
  settings: TablerinoSettings = {
    checkboxes: false,
    clickableRows: true,
    routerLinkKey: 'affiliate_id',
    routerLinkPrefix: '/affiliates/details/'
  };

  modalReference: NgbModalRef;
  affiliates: AffiliateResponse[] = [];
  isLoading: boolean = false;
  paginationSubject: BehaviorSubject<PaginationContainer> = new BehaviorSubject<PaginationContainer>({page: 1, limit: 25, paginate: 1, totalPages: 0, totalItems: 0});
  headerFiltersSubject: BehaviorSubject<HeaderFiltersContainer> = new BehaviorSubject<HeaderFiltersContainer>({filters: [], init: true});

  partnersEnabled: boolean = false;
  accountingEnabled: boolean = false;
  superAdminAccess: boolean = false;

  orderBy: string = '';
  orderByDirection: string = 'desc';

  constructor(private route: ActivatedRoute, private modalService: NgbModal, private affiliateService: AffiliateService, private router: Router, private storageService: StorageService, private authService: AuthService) { }

  ngOnInit(): void {
    this.superAdminAccess = this.authService.checkComponentAccess('SUP-0');
    this.private = this.route.snapshot.data['private'];
    this.storageService.partnersEnabled$.subscribe((partnersEnabled) => {
      this.partnersEnabled = partnersEnabled;
    });
    this.storageService.accountingEnabled$.subscribe((accountingEnabled) => {
      this.accountingEnabled = accountingEnabled;
    });
    this.initColumns();
    this.initFilters();
    this.fetchAffiliates();
    this.paginationSubject.pipe(pairwise()).subscribe((paginationDetails) => {
      if (paginationDetails[0].page !== paginationDetails[1].page) {
        this.fetchAffiliates();
      }
    });
    this.headerFiltersSubject.subscribe((headerFilters) => {
      if (!headerFilters.init) {
        this.fetchAffiliates();
      }
    });

  }

  initColumns() {
    let columns: TablerinoColumn[] = []
    if (!this.private) {
      columns = [
        {
          name: 'name',
          labelKey: 'affiliates.list.name',
          formatter: (aff: AffiliateResponse) => aff.name,
          sort: true,
          visible: true,
        },
        {
          name: 'orgNumber',
          labelKey: 'affiliates.list.orgNumber',
          formatter: (aff: AffiliateResponse) => aff.organisation_number,
          sort: false,
          visible: true,
        },
        {
          name: 'customer',
          labelKey: 'affiliates.list.customer',
          formatter: (aff: AffiliateResponse) => this.formatDotIcon(aff.is_customer === 1),
          sort: false,
          visible: true,
        },
        {
          name: 'partner',
          labelKey: 'affiliates.list.partner',
          formatter: (aff: AffiliateResponse) => this.formatDotIcon(aff.is_partner === 1),
          sort: false,
          visible: true,
        },
        {
          name: 'subContractor',
          labelKey: 'affiliates.list.subContractor',
          formatter: (aff: AffiliateResponse) => this.formatDotIcon(aff.is_subcontractor === 1),
          sort: false,
          visible: true,
        },
      ];
    } else {
      columns = [
        {
          name: 'name',
          labelKey: 'affiliates.list.name.private',
          formatter: (aff: AffiliateResponse) => aff.name,
          sort: true,
          visible: true,
        },
        {
          name: 'email',
          labelKey: 'affiliates.list.email',
          formatter: (aff: AffiliateResponse) => aff.email,
          sort: false,
          visible: true,
        },
        {
          name: 'phone',
          labelKey: 'affiliates.list.phone',
          formatter: (aff: AffiliateResponse) => aff.phone,
          sort: false,
          visible: true,
        },
      ];
    }

    if (!this.partnersEnabled) {
      columns = columns.filter(col => col.name !== 'partner');
    }
    this.columnsSubject.next(columns);
  }

  initFilters() {
    let headerFilters: HeaderFilterComponent[] = [];
    if (!this.private) {
      headerFilters = [
        {
          parameterName: 'partners_only',
          translationKey: 'affiliates.list.partner',
          active: false,
        },
        {
          parameterName: 'customers_only',
          translationKey: 'affiliates.list.customer',
          active: false,
        },
        {
          parameterName: 'subcontractors_only',
          translationKey: 'affiliates.list.subContractor',
          active: false,
        },
      ];
    }

    if (!this.partnersEnabled) {
      headerFilters = headerFilters.filter(f => f.parameterName !== 'partners_only');
    }

    this.headerFiltersSubject.next({filters: headerFilters, init: true});
  }

  formatDotIcon(enable: boolean) {
    return enable ? '<i class="fa-solid fa-check text-success"></i>' : ''
  }


  fetchAffiliates(searchTerm?: string) {
    this.isLoading = true;

    let params: _CRM_AFF_1 = {
      paginate: 1,
      page: this.paginationSubject.value.page,
      limit: this.paginationSubject.value.limit,
      partners_only: this.headerFiltersSubject.value.filters.find(f => f.parameterName === 'partners_only')?.active ? 1 : 0,
      customers_only: this.headerFiltersSubject.value.filters.find(f => f.parameterName === 'customers_only')?.active ? 1 : 0,
      subcontractors_only: this.headerFiltersSubject.value.filters.find(f => f.parameterName === 'subcontractors_only')?.active ? 1 : 0,
      business_only: this.private ? 0 : 1,
      private_only: this.private ? 1 : 0,
      order_by: this.orderBy,
      order_direction: this.orderByDirection,
      search_string: searchTerm,
    }
    // Cancel pending request if there is one
    this.destroy$.next();

    this.affiliateService.getAffiliates(params).pipe(
      debounceTime(400), // Adjust debounce time as needed
      takeUntil(this.destroy$) // Cancel the request if component is destroyed
    ).subscribe(
      (res) => {
        this.affiliates = res.data;
        this.isLoading = false;
        this.paginationSubject.next({
          ...this.paginationSubject.value,
          totalPages: res.total_pages,
          totalItems: res.total_items
        });
      },
      (error) => {
        console.log(error);
        this.isLoading = false;
      }
    );
  }

  onAddAffiliate() {
    if (this.private) {
      this.modalService.open(PrivateCustomersModalComponent, {size: 'lg'});
    } else {
      this.modalService.open(AddAffiliateModalComponent, {size: 'lg'});
    }
  }

  onSort(event: SortEvent) {
    this.orderBy = event.column;
    this.orderByDirection = event.direction;

    this.fetchAffiliates();
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  rowClicked(event: AffiliateResponse | any) {
    this.router.navigateByUrl("affiliates/details/" + event.affiliate_id)
  }

  openImportAffiliatesModal(accounting: boolean) {
    let modalRef = this.modalService.open(ImportCustomersModalComponent, {size: 'xl'});
    modalRef.result.then((res) => {
      if (res) {
        this.fetchAffiliates();
      }
    });
  }

}
