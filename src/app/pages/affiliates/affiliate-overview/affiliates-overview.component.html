<app-page-header
    [loading]="isLoading"
    [pageName]="(private ? 'affiliates.itemName.private' : 'affiliates.itemName') | translate"
    [breadcrumb]='[{"name": "affiliates.itemName" | translate, "routerPath": ""}]'
    [showButton]="true"
    [btnText]="(private ? 'affiliates.addNew.private' : 'affiliates.addNew') | translate"
  >
  <div class="d-flex gap-2">
    <app-button
      *ngIf="accountingEnabled && superAdminAccess"
      [translationKey]="'affiliates.importCustomers'"
      (buttonClick)="openImportAffiliatesModal(true)"
    ></app-button>
    <app-button
      [translationKey]="'affiliates.addNew' + (private ? '.private' : '')"
      (buttonClick)="onAddAffiliate()"
    ></app-button>
  </div>
</app-page-header>

<app-tablerino-complete
  [tableName]="private ? 'private-affiliates-list' : 'affiliates-list'"
  [disableSort]="true"
  [disableDrag]="true"
  [showAllFilter]="true"
  [columnsSubject]="columnsSubject"
  [tableData]="affiliates"
  [settings]="settings"
  [loading]="isLoading"
  [showQuickSearch]="true"
  [headerFiltersContainerSubject]="headerFiltersSubject"
  [paginationSubject]="paginationSubject"
  (quickSearchEmitter)="fetchAffiliates($event)"
  (rowClickedEmitter)="rowClicked($event)"
></app-tablerino-complete>

